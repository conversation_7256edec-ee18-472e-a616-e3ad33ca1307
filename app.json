{"expo": {"name": "ControllOne", "slug": "ControllOne", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "controllone", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.harsh.ControllOne"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.harsh.ControllOne"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 250, "resizeMode": "contain", "backgroundColor": "#ffffff", "radius": 0}], "expo-secure-store"], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "11492803-021c-459d-bbf6-ef17fae6053d"}}}}