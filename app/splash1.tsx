import { router } from 'expo-router';
import React, { useEffect } from 'react';
import { Image, SafeAreaView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import LanguageSwitcher from '../components/LanguageSwitcher';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';

const SplashScreen1 = () => {
  const { t } = useLanguage();
  const { isAuthenticated, isLoading } = useAuth();

  // Check authentication status and redirect if already logged in
  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      router.replace('/(tabs)');
    }
  }, [isAuthenticated, isLoading]);

  const handleNext = () => {
    router.push('/splash2');
  };

  const handleSkip = () => {
    router.push('/login');
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Top Bar with Language Switcher and Skip <PERSON>ton */}
      <View style={styles.topBar}>
        <LanguageSwitcher />
        <TouchableOpacity onPress={handleSkip} style={styles.skipButton}>
          <Text style={styles.skipText}>
            {t('buttons.skip')}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Main Content */}
      <View style={styles.mainContent}>
        {/* Image */}
        <View style={styles.imageContainer}>
          <Image
            source={require('../assets/images/splash1.webp')}
            style={styles.image}
            resizeMode="contain"
          />
        </View>

        {/* Heading */}
        <Text style={styles.heading}>
          {t('splash1.heading')}
        </Text>

        {/* Paragraph */}
        <Text style={styles.paragraph}>
          {t('splash1.paragraph')}
        </Text>
      </View>

      {/* Next Button */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          onPress={handleNext}
          style={styles.nextButton}
          activeOpacity={0.8}
        >
          <Text style={styles.nextButtonText}>
            {t('buttons.next')}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  topBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  skipButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  skipText: {
    color: '#6B7280',
    fontSize: 16,
    fontWeight: '500',
  },
  mainContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  imageContainer: {
    marginBottom: 30,
  },
  image: {
    width: 400,
    height: 250,
  },
  heading: {
    fontSize: 30,
    fontWeight: 'bold',
    color: '#111827',
    textAlign: 'center',
    marginBottom: 24,
  },
  paragraph: {
    fontSize: 18,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 48,
  },
  buttonContainer: {
    padding: 24,
  },
  nextButton: {
    backgroundColor: '#F54619',
    borderRadius: 8,
    paddingVertical: 16,
    paddingHorizontal: 32,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  nextButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default SplashScreen1;
