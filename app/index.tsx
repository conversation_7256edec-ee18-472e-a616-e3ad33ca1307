import { Redirect } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, View } from 'react-native';
import { useAuth } from '../contexts/AuthContext';

export default function Index() {
  const { isAuthenticated, isLoading } = useAuth();
  const [initialCheckComplete, setInitialCheckComplete] = useState(false);

  useEffect(() => {
    // Wait for the initial auth check to complete
    if (!isLoading) {
      setInitialCheckComplete(true);
    }
  }, [isLoading]);

  // Show loading spinner while checking authentication
  if (!initialCheckComplete) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#ffffff' }}>
        <ActivityIndicator size="large" color="#F54619" />
      </View>
    );
  }

  // Redirect based on authentication status
  if (isAuthenticated) {
    // User is authenticated, redirect to main app
    return <Redirect href="/(tabs)" />;
  } else {
    // User is not authenticated, redirect to first splash screen
    return <Redirect href="/splash1" />;
  }
}
