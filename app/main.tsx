import React, { useEffect } from 'react';
import { router } from 'expo-router';
import { MainLayout } from '../components/layout/MainLayout';
import { useAuth } from '../contexts/AuthContext';

/**
 * Main screen component with new navigation layout
 */
export default function MainScreen() {
  const { isAuthenticated, isLoading } = useAuth();

  // Check authentication and redirect if not logged in
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.replace('/login');
    }
  }, [isAuthenticated, isLoading]);

  // Show loading or redirect if not authenticated
  if (isLoading || !isAuthenticated) {
    return null;
  }

  /**
   * Handle notification press
   */
  const handleNotificationPress = () => {
    // TODO: Implement notification functionality
    console.log('Notification pressed');
  };

  /**
   * Handle profile press
   */
  const handleProfilePress = () => {
    // TODO: Implement profile functionality
    console.log('Profile pressed');
  };

  return (
    <MainLayout
      onNotificationPress={handleNotificationPress}
      onProfilePress={handleProfilePress}
    />
  );
}
