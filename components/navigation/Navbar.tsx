import React from 'react';
import { Image, SafeAreaView, TouchableOpacity, View } from 'react-native';
import { styled } from 'nativewind';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useNavigation } from '../../contexts/NavigationContext';
import { useLanguage } from '../../contexts/LanguageContext';

// Styled components using nativewind
const StyledSafeAreaView = styled(SafeAreaView);
const StyledView = styled(View);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledImage = styled(Image);

/**
 * Navbar component props
 */
interface NavbarProps {
  onNotificationPress?: () => void;
  onProfilePress?: () => void;
}

/**
 * Top navigation bar component
 */
export const Navbar: React.FC<NavbarProps> = ({
  onNotificationPress,
  onProfilePress,
}) => {
  const { toggleSidebar } = useNavigation();
  const { t } = useLanguage();

  return (
    <StyledSafeAreaView className="bg-white">
      <StyledView className="flex-row items-center justify-between px-4 py-3 bg-white border-b border-gray-200 shadow-sm">
        {/* Left side - Menu button */}
        <StyledTouchableOpacity
          onPress={toggleSidebar}
          className="w-12 h-12 items-center justify-center rounded-lg bg-gray-50 active:bg-gray-100"
          accessibilityLabel={t('navigation.menu')}
          accessibilityRole="button"
        >
          <MaterialIcons name="menu" size={24} color="#374151" />
        </StyledTouchableOpacity>

        {/* Center - Logo */}
        <StyledView className="flex-1 items-center justify-center mx-4">
          <StyledImage
            source={require('../../assets/images/icon.png')}
            className="w-10 h-10"
            resizeMode="contain"
            accessibilityLabel="ControllOne Logo"
          />
        </StyledView>

        {/* Right side - Notification and Profile buttons */}
        <StyledView className="flex-row items-center space-x-2">
          {/* Notification button */}
          <StyledTouchableOpacity
            onPress={onNotificationPress}
            className="w-12 h-12 items-center justify-center rounded-lg bg-gray-50 active:bg-gray-100"
            accessibilityLabel={t('navigation.notifications')}
            accessibilityRole="button"
          >
            <MaterialIcons name="notifications" size={24} color="#374151" />
          </StyledTouchableOpacity>

          {/* Profile button */}
          <StyledTouchableOpacity
            onPress={onProfilePress}
            className="w-12 h-12 items-center justify-center rounded-lg bg-gray-50 active:bg-gray-100"
            accessibilityLabel={t('navigation.profile')}
            accessibilityRole="button"
          >
            <MaterialIcons name="account-circle" size={24} color="#374151" />
          </StyledTouchableOpacity>
        </StyledView>
      </StyledView>
    </StyledSafeAreaView>
  );
};
