import React from 'react';
import {
  Animated,
  Dimensions,
  Modal,
  ScrollView,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { styled } from 'nativewind';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useNavigation, NavigationItem } from '../../contexts/NavigationContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { useAuth } from '../../contexts/AuthContext';

// Styled components using nativewind
const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledScrollView = styled(ScrollView);

const { width: screenWidth } = Dimensions.get('window');
const SIDEBAR_WIDTH = screenWidth * 0.8; // 80% of screen width

/**
 * Sidebar component props
 */
interface SidebarProps {
  onNavigate?: (route: string) => void;
}

/**
 * Sidebar menu item component
 */
interface SidebarItemProps {
  item: NavigationItem;
  isActive: boolean;
  onPress: () => void;
}

const SidebarItem: React.FC<SidebarItemProps> = ({ item, isActive, onPress }) => {
  const { t } = useLanguage();

  return (
    <StyledTouchableOpacity
      onPress={onPress}
      className={`flex-row items-center px-6 py-4 mx-4 my-1 rounded-lg ${
        isActive ? 'bg-primary' : 'bg-transparent active:bg-gray-100'
      }`}
      accessibilityRole="button"
      accessibilityLabel={t(item.titleKey)}
    >
      <MaterialIcons
        name={item.icon as any}
        size={24}
        color={isActive ? '#FFFFFF' : '#6B7280'}
        style={{ marginRight: 12 }}
      />
      <StyledText
        className={`text-base font-medium ${
          isActive ? 'text-white' : 'text-gray-700'
        }`}
      >
        {t(item.titleKey)}
      </StyledText>
    </StyledTouchableOpacity>
  );
};

/**
 * Sliding sidebar component
 */
export const Sidebar: React.FC<SidebarProps> = ({ onNavigate }) => {
  const {
    isSidebarOpen,
    closeSidebar,
    navigationItems,
    currentRoute,
    setCurrentRoute,
  } = useNavigation();
  const { t } = useLanguage();
  const { logout } = useAuth();

  /**
   * Handle navigation item press
   */
  const handleItemPress = async (item: NavigationItem) => {
    if (item.id === 'logout') {
      // Handle logout
      try {
        await logout();
        closeSidebar();
      } catch (error) {
        console.error('Logout error:', error);
      }
    } else if (item.id === 'settings') {
      // Settings - no action for now
      closeSidebar();
    } else if (item.url) {
      // Navigate to route
      setCurrentRoute(item.id);
      onNavigate?.(item.url);
      closeSidebar();
    }
  };

  // Split navigation items into main items and bottom items
  const mainItems = navigationItems.filter(
    item => !['settings', 'logout'].includes(item.id)
  );
  const bottomItems = navigationItems.filter(item =>
    ['settings', 'logout'].includes(item.id)
  );

  return (
    <Modal
      visible={isSidebarOpen}
      transparent
      animationType="none"
      onRequestClose={closeSidebar}
    >
      <StyledView className="flex-1 flex-row">
        {/* Overlay */}
        <TouchableWithoutFeedback onPress={closeSidebar}>
          <StyledView className="flex-1 bg-black/50" />
        </TouchableWithoutFeedback>

        {/* Sidebar */}
        <StyledView
          className="bg-white h-full shadow-2xl"
          style={{ width: SIDEBAR_WIDTH }}
        >
          {/* Header */}
          <StyledView className="flex-row items-center justify-between px-6 py-6 border-b border-gray-200">
            {/* Logo and title */}
            <StyledView className="flex-row items-center flex-1">
              <MaterialIcons name="location-on" size={32} color="#F54619" />
              <StyledText className="text-xl font-bold text-gray-800 ml-3">
                ControllOne.it
              </StyledText>
            </StyledView>

            {/* Close button */}
            <StyledTouchableOpacity
              onPress={closeSidebar}
              className="w-10 h-10 items-center justify-center rounded-lg active:bg-gray-100"
              accessibilityLabel={t('navigation.close')}
              accessibilityRole="button"
            >
              <MaterialIcons name="close" size={24} color="#374151" />
            </StyledTouchableOpacity>
          </StyledView>

          {/* Menu items */}
          <StyledView className="flex-1">
            <StyledScrollView
              className="flex-1 py-4"
              showsVerticalScrollIndicator={false}
            >
              {/* Main navigation items */}
              {mainItems.map(item => (
                <SidebarItem
                  key={item.id}
                  item={item}
                  isActive={currentRoute === item.id}
                  onPress={() => handleItemPress(item)}
                />
              ))}
            </StyledScrollView>

            {/* Bottom section */}
            <StyledView className="border-t border-gray-200 py-4">
              {/* Settings and Logout */}
              {bottomItems.map(item => (
                <SidebarItem
                  key={item.id}
                  item={item}
                  isActive={false}
                  onPress={() => handleItemPress(item)}
                />
              ))}

              {/* Copyright */}
              <StyledView className="px-6 py-4">
                <StyledText className="text-sm text-gray-500 text-center">
                  {t('navigation.copyright')}
                </StyledText>
              </StyledView>
            </StyledView>
          </StyledView>
        </StyledView>
      </StyledView>
    </Modal>
  );
};
