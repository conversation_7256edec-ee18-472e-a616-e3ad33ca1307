import React, { useEffect, useState } from 'react';
import { ActivityIndicator, View } from 'react-native';
import { WebView } from 'react-native-webview';
import { styled } from 'nativewind';
import { Navbar } from '../navigation/Navbar';
import { Sidebar } from '../navigation/Sidebar';
import { useNavigation } from '../../contexts/NavigationContext';
import { useAuth } from '../../contexts/AuthContext';

// Styled components using nativewind
const StyledView = styled(View);

/**
 * Main layout component props
 */
interface MainLayoutProps {
  onNotificationPress?: () => void;
  onProfilePress?: () => void;
}

/**
 * Main layout component that combines navbar, sidebar, and content area
 */
export const MainLayout: React.FC<MainLayoutProps> = ({
  onNotificationPress,
  onProfilePress,
}) => {
  const { currentRoute, getWebViewUrl } = useNavigation();
  const { token } = useAuth();
  const [webViewUrl, setWebViewUrl] = useState<string>('');

  /**
   * Update WebView URL when route or token changes
   */
  useEffect(() => {
    if (token) {
      const url = getWebViewUrl(currentRoute, token);
      setWebViewUrl(url);
    }
  }, [currentRoute, token, getWebViewUrl]);

  /**
   * Handle navigation from sidebar
   */
  const handleNavigate = (route: string) => {
    if (token) {
      const url = getWebViewUrl(route, token);
      setWebViewUrl(url);
    }
  };

  /**
   * Handle notification press
   */
  const handleNotificationPress = () => {
    onNotificationPress?.();
    // TODO: Implement notification functionality
  };

  /**
   * Handle profile press
   */
  const handleProfilePress = () => {
    onProfilePress?.();
    // TODO: Implement profile functionality
  };

  return (
    <StyledView className="flex-1 bg-white">
      {/* Navbar */}
      <Navbar
        onNotificationPress={handleNotificationPress}
        onProfilePress={handleProfilePress}
      />

      {/* Main content area */}
      <StyledView className="flex-1 px-4 py-4">
        {webViewUrl ? (
          <WebView
            source={{ uri: webViewUrl }}
            style={{
              flex: 1,
              borderRadius: 10,
              overflow: 'hidden',
            }}
            startInLoadingState={true}
            renderLoading={() => (
              <StyledView className="absolute inset-0 items-center justify-center bg-white z-10">
                <ActivityIndicator size="large" color="#F54619" />
              </StyledView>
            )}
            onError={(syntheticEvent) => {
              const { nativeEvent } = syntheticEvent;
              console.error('WebView error: ', nativeEvent);
            }}
            onHttpError={(syntheticEvent) => {
              const { nativeEvent } = syntheticEvent;
              console.error('WebView HTTP error: ', nativeEvent);
            }}
            onLoadStart={() => {
              console.log('WebView loading started for:', webViewUrl);
            }}
            onLoadEnd={() => {
              console.log('WebView loading ended for:', webViewUrl);
            }}
            // Allow navigation within the same domain
            onShouldStartLoadWithRequest={(request) => {
              const { url } = request;
              // Allow navigation within the ControllOne domain
              return url.includes('piattaforma.controllone.it');
            }}
            // Security settings
            javaScriptEnabled={true}
            domStorageEnabled={true}
            startInLoadingState={true}
            scalesPageToFit={true}
            bounces={false}
            scrollEnabled={true}
            // iOS specific settings
            allowsInlineMediaPlayback={true}
            mediaPlaybackRequiresUserAction={false}
            // Android specific settings
            mixedContentMode="compatibility"
            thirdPartyCookiesEnabled={true}
          />
        ) : (
          <StyledView className="flex-1 items-center justify-center">
            <ActivityIndicator size="large" color="#F54619" />
          </StyledView>
        )}
      </StyledView>

      {/* Sidebar */}
      <Sidebar onNavigate={handleNavigate} />
    </StyledView>
  );
};
