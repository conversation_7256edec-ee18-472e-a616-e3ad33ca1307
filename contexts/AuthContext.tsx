import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { ApiError } from '../constants/Api';
import { authService } from '../services/authService';
import { secureStorage, storage } from '../utils/storage';

/**
 * Authentication state interface
 */
interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  token: string | null;
  user: any | null;
}

/**
 * Authentication context interface
 */
interface AuthContextType extends AuthState {
  login: (email: string, password: string, language: string) => Promise<{ success: boolean; message: string }>;
  logout: () => Promise<void>;
  forgotPassword: (email: string, language: string) => Promise<{ success: boolean; message: string }>;
  checkAuthStatus: () => Promise<void>;
}

/**
 * Create Authentication Context
 */
const AuthContext = createContext<AuthContextType | undefined>(undefined);

/**
 * Hook to use Authentication Context
 */
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

/**
 * Authentication Provider Props
 */
interface AuthProviderProps {
  children: ReactNode;
}

/**
 * Authentication Provider Component
 */
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    isLoading: true,
    token: null,
    user: null,
  });

  /**
   * Check authentication status on app startup
   */
  const checkAuthStatus = async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true }));

      // First check if we have a stored token
      const token = await secureStorage.getToken();

      if (!token) {
        // No token found, user is not authenticated
        console.log('No token found, redirecting to splash screen');
        setAuthState({
          isAuthenticated: false,
          isLoading: false,
          token: null,
          user: null,
        });
        return;
      }

      // Token exists, validate it with the server
      console.log('Token found, validating with server...');
      const validationResult = await authService.validateToken(token);

      if (validationResult.valid) {
        // Token is valid, user is authenticated
        const user = await storage.getUserData();
        console.log('Token is valid, user authenticated');
        setAuthState({
          isAuthenticated: true,
          isLoading: false,
          token,
          user,
        });
      } else {
        // Token is invalid (401 or other error), clear storage and redirect to login
        console.log('Token is invalid, clearing storage and redirecting to login');
        await storage.clearAll();
        setAuthState({
          isAuthenticated: false,
          isLoading: false,
          token: null,
          user: null,
        });
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      // On any error, clear storage and set as unauthenticated
      await storage.clearAll();
      setAuthState({
        isAuthenticated: false,
        isLoading: false,
        token: null,
        user: null,
      });
    }
  };

  /**
   * Login function
   */
  const login = async (email: string, password: string, language: string): Promise<{ success: boolean; message: string }> => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true }));

      // Validate form
      const validation = authService.validateLoginForm(email, password);
      if (!validation.isValid) {
        setAuthState(prev => ({ ...prev, isLoading: false }));
        return {
          success: false,
          message: validation.errors.join(', '),
        };
      }

      // Make login request
      const response = await authService.login(email, password, language);

      // Store token and user data
      await secureStorage.setToken(response.token);
      await storage.setUserData({ email });
      await storage.setLanguage(language);

      // Update auth state
      setAuthState({
        isAuthenticated: true,
        isLoading: false,
        token: response.token,
        user: { email },
      });

      console.log('Login successful');
      return {
        success: true,
        message: response.message,
      };
    } catch (error) {
      console.error('Login error:', error);
      setAuthState(prev => ({ ...prev, isLoading: false }));
      
      const apiError = error as ApiError;
      return {
        success: false,
        message: apiError.message || 'Login failed. Please try again.',
      };
    }
  };

  /**
   * Logout function
   */
  const logout = async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true }));

      // Clear all stored data
      await storage.clearAll();

      // Update auth state
      setAuthState({
        isAuthenticated: false,
        isLoading: false,
        token: null,
        user: null,
      });

      console.log('Logout successful');
    } catch (error) {
      console.error('Logout error:', error);
      // Even if there's an error, we should still clear the state
      setAuthState({
        isAuthenticated: false,
        isLoading: false,
        token: null,
        user: null,
      });
    }
  };

  /**
   * Forgot password function
   */
  const forgotPassword = async (email: string, language: string): Promise<{ success: boolean; message: string }> => {
    try {
      // Validate form
      const validation = authService.validateForgotPasswordForm(email);
      if (!validation.isValid) {
        return {
          success: false,
          message: validation.errors.join(', '),
        };
      }

      // Make forgot password request
      const response = await authService.forgotPassword(email, language);

      console.log('Forgot password request successful');
      return {
        success: true,
        message: response.message,
      };
    } catch (error) {
      console.error('Forgot password error:', error);
      
      const apiError = error as ApiError;
      return {
        success: false,
        message: apiError.message || 'Failed to send reset email. Please try again.',
      };
    }
  };

  /**
   * Check auth status on component mount
   */
  useEffect(() => {
    checkAuthStatus();
  }, []);

  /**
   * Context value
   */
  const contextValue: AuthContextType = {
    ...authState,
    login,
    logout,
    forgotPassword,
    checkAuthStatus,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};
